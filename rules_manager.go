package main

import (
	"fmt"
	"sync"
)

// SecurityRule 安全检查规则结构体
type SecurityRule struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Category    string            `json:"category"`
	Level       string            `json:"level"`
	Description string            `json:"description"`
	Command     string            `json:"command"`
	CheckType   string            `json:"check_type"`
	Expected    interface{}       `json:"expected"`
	Enabled     bool              `json:"enabled"`
	Tags        []string          `json:"tags"`
	Metadata    map[string]string `json:"metadata"`
}

// RuleCategory 规则分类
type RuleCategory struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Icon        string `json:"icon"`
}

// RuleManager 规则管理器接口
type RuleManager interface {
	GetAllRules() []SecurityRule
	GetRulesByCategory(category string) []SecurityRule
	GetRuleByID(id string) (*SecurityRule, error)
	GetEnabledRules() []SecurityRule
	UpdateRuleStatus(id string, enabled bool) error
	GetCategories() []RuleCategory
}

// BuiltinRuleManager 内置规则管理器
type BuiltinRuleManager struct {
	rules      map[string]*SecurityRule
	categories map[string]*RuleCategory
	mutex      sync.RWMutex
}

// NewBuiltinRuleManager 创建内置规则管理器
func NewBuiltinRuleManager() *BuiltinRuleManager {
	manager := &BuiltinRuleManager{
		rules:      make(map[string]*SecurityRule),
		categories: make(map[string]*RuleCategory),
	}
	manager.initBuiltinRules()
	return manager
}

// initBuiltinRules 初始化内置规则
func (rm *BuiltinRuleManager) initBuiltinRules() {
	// 初始化规则分类
	rm.initCategories()
	
	// 初始化身份鉴别类规则
	rm.initAuthenticationRules()
	
	// 初始化访问控制类规则
	rm.initAccessControlRules()
	
	// 初始化安全审计类规则
	rm.initAuditRules()
	
	// 初始化系统安全类规则
	rm.initSystemSecurityRules()
	
	// 初始化网络安全类规则
	rm.initNetworkSecurityRules()
}

// initCategories 初始化规则分类
func (rm *BuiltinRuleManager) initCategories() {
	categories := []RuleCategory{
		{
			ID:          "authentication",
			Name:        "身份鉴别",
			Description: "用户身份验证和账户管理相关检查",
			Icon:        "🔐",
		},
		{
			ID:          "access_control",
			Name:        "访问控制",
			Description: "文件权限和访问控制相关检查",
			Icon:        "🛡️",
		},
		{
			ID:          "audit",
			Name:        "安全审计",
			Description: "系统审计和日志管理相关检查",
			Icon:        "📋",
		},
		{
			ID:          "system_security",
			Name:        "系统安全",
			Description: "系统配置和安全加固相关检查",
			Icon:        "⚙️",
		},
		{
			ID:          "network_security",
			Name:        "网络安全",
			Description: "网络配置和服务安全相关检查",
			Icon:        "🌐",
		},
	}
	
	for _, category := range categories {
		rm.categories[category.ID] = &category
	}
}

// initAuthenticationRules 初始化身份鉴别类规则
func (rm *BuiltinRuleManager) initAuthenticationRules() {
	rules := []SecurityRule{
		{
			ID:          "auth_001",
			Name:        "密码复杂度策略检查",
			Category:    "authentication",
			Level:       "high",
			Description: "检查系统密码复杂度策略配置是否符合等保要求",
			Command:     "grep -E '^(minlen|dcredit|ucredit|lcredit|ocredit)' /etc/security/pwquality.conf",
			CheckType:   "regex",
			Expected:    map[string]interface{}{"minlen": 8, "dcredit": -1, "ucredit": -1, "lcredit": -1, "ocredit": -1},
			Enabled:     true,
			Tags:        []string{"password", "policy", "等保三级"},
			Metadata: map[string]string{
				"standard": "GB/T 22239-2019",
				"level":    "三级",
				"type":     "身份鉴别",
			},
		},
		{
			ID:          "auth_002",
			Name:        "账户锁定策略检查",
			Category:    "authentication",
			Level:       "high",
			Description: "检查账户锁定策略配置，防止暴力破解攻击",
			Command:     "grep -E '^(deny|unlock_time)' /etc/security/faillock.conf",
			CheckType:   "regex",
			Expected:    map[string]interface{}{"deny": 5, "unlock_time": 300},
			Enabled:     true,
			Tags:        []string{"account", "lockout", "等保三级"},
			Metadata: map[string]string{
				"standard": "GB/T 22239-2019",
				"level":    "三级",
				"type":     "身份鉴别",
			},
		},
		{
			ID:          "auth_003",
			Name:        "用户账户唯一性检查",
			Category:    "authentication",
			Level:       "medium",
			Description: "检查系统中是否存在重复的用户账户",
			Command:     "cut -d: -f1 /etc/passwd | sort | uniq -d",
			CheckType:   "empty",
			Expected:    "",
			Enabled:     true,
			Tags:        []string{"user", "unique", "等保三级"},
			Metadata: map[string]string{
				"standard": "GB/T 22239-2019",
				"level":    "三级",
				"type":     "身份鉴别",
			},
		},
		{
			ID:          "auth_004",
			Name:        "特权用户管理检查",
			Category:    "authentication",
			Level:       "high",
			Description: "检查具有sudo权限的用户账户",
			Command:     "grep -v '^#' /etc/sudoers | grep -v '^$'",
			CheckType:   "contains",
			Expected:    "root ALL=(ALL:ALL) ALL",
			Enabled:     true,
			Tags:        []string{"sudo", "privilege", "等保三级"},
			Metadata: map[string]string{
				"standard": "GB/T 22239-2019",
				"level":    "三级",
				"type":     "身份鉴别",
			},
		},
		{
			ID:          "auth_005",
			Name:        "登录失败处理机制检查",
			Category:    "authentication",
			Level:       "medium",
			Description: "检查登录失败处理机制配置",
			Command:     "grep -E '^auth.*pam_faillock' /etc/pam.d/system-auth",
			CheckType:   "exists",
			Expected:    true,
			Enabled:     true,
			Tags:        []string{"login", "failure", "等保三级"},
			Metadata: map[string]string{
				"standard": "GB/T 22239-2019",
				"level":    "三级",
				"type":     "身份鉴别",
			},
		},
	}
	
	for _, rule := range rules {
		rm.rules[rule.ID] = &rule
	}
}

// GetAllRules 获取所有规则
func (rm *BuiltinRuleManager) GetAllRules() []SecurityRule {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	
	rules := make([]SecurityRule, 0, len(rm.rules))
	for _, rule := range rm.rules {
		rules = append(rules, *rule)
	}
	return rules
}

// GetRulesByCategory 根据分类获取规则
func (rm *BuiltinRuleManager) GetRulesByCategory(category string) []SecurityRule {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	
	var rules []SecurityRule
	for _, rule := range rm.rules {
		if rule.Category == category {
			rules = append(rules, *rule)
		}
	}
	return rules
}

// GetRuleByID 根据ID获取规则
func (rm *BuiltinRuleManager) GetRuleByID(id string) (*SecurityRule, error) {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	
	rule, exists := rm.rules[id]
	if !exists {
		return nil, fmt.Errorf("规则 %s 不存在", id)
	}
	return rule, nil
}

// GetEnabledRules 获取启用的规则
func (rm *BuiltinRuleManager) GetEnabledRules() []SecurityRule {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	
	var rules []SecurityRule
	for _, rule := range rm.rules {
		if rule.Enabled {
			rules = append(rules, *rule)
		}
	}
	return rules
}

// UpdateRuleStatus 更新规则状态
func (rm *BuiltinRuleManager) UpdateRuleStatus(id string, enabled bool) error {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()
	
	rule, exists := rm.rules[id]
	if !exists {
		return fmt.Errorf("规则 %s 不存在", id)
	}
	
	rule.Enabled = enabled
	return nil
}

// GetCategories 获取所有分类
func (rm *BuiltinRuleManager) GetCategories() []RuleCategory {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()

	categories := make([]RuleCategory, 0, len(rm.categories))
	for _, category := range rm.categories {
		categories = append(categories, *category)
	}
	return categories
}

// initAccessControlRules 初始化访问控制类规则
func (rm *BuiltinRuleManager) initAccessControlRules() {
	rules := []SecurityRule{
		{
			ID:          "access_001",
			Name:        "关键系统文件权限检查",
			Category:    "access_control",
			Level:       "high",
			Description: "检查关键系统文件的权限配置是否安全",
			Command:     "ls -la /etc/passwd /etc/shadow /etc/group /etc/gshadow",
			CheckType:   "permission",
			Expected:    map[string]string{"/etc/passwd": "644", "/etc/shadow": "600", "/etc/group": "644", "/etc/gshadow": "600"},
			Enabled:     true,
			Tags:        []string{"file", "permission", "等保三级"},
			Metadata: map[string]string{
				"standard": "GB/T 22239-2019",
				"level":    "三级",
				"type":     "访问控制",
			},
		},
		{
			ID:          "access_002",
			Name:        "sudo配置检查",
			Category:    "access_control",
			Level:       "high",
			Description: "检查sudo配置文件的安全性",
			Command:     "ls -la /etc/sudoers",
			CheckType:   "permission",
			Expected:    "440",
			Enabled:     true,
			Tags:        []string{"sudo", "permission", "等保三级"},
			Metadata: map[string]string{
				"standard": "GB/T 22239-2019",
				"level":    "三级",
				"type":     "访问控制",
			},
		},
		{
			ID:          "access_003",
			Name:        "用户权限最小化检查",
			Category:    "access_control",
			Level:       "medium",
			Description: "检查用户是否遵循最小权限原则",
			Command:     "awk -F: '$3 == 0 {print $1}' /etc/passwd",
			CheckType:   "count",
			Expected:    1,
			Enabled:     true,
			Tags:        []string{"user", "privilege", "等保三级"},
			Metadata: map[string]string{
				"standard": "GB/T 22239-2019",
				"level":    "三级",
				"type":     "访问控制",
			},
		},
		{
			ID:          "access_004",
			Name:        "远程访问控制检查",
			Category:    "access_control",
			Level:       "high",
			Description: "检查SSH远程访问控制配置",
			Command:     "grep -E '^(PermitRootLogin|PasswordAuthentication|PubkeyAuthentication)' /etc/ssh/sshd_config",
			CheckType:   "contains",
			Expected:    []string{"PermitRootLogin no", "PasswordAuthentication yes", "PubkeyAuthentication yes"},
			Enabled:     true,
			Tags:        []string{"ssh", "remote", "等保三级"},
			Metadata: map[string]string{
				"standard": "GB/T 22239-2019",
				"level":    "三级",
				"type":     "访问控制",
			},
		},
		{
			ID:          "access_005",
			Name:        "文件和目录权限检查",
			Category:    "access_control",
			Level:       "medium",
			Description: "检查重要目录的权限配置",
			Command:     "ls -ld /tmp /var/tmp /home",
			CheckType:   "permission",
			Expected:    map[string]string{"/tmp": "1777", "/var/tmp": "1777", "/home": "755"},
			Enabled:     true,
			Tags:        []string{"directory", "permission", "等保三级"},
			Metadata: map[string]string{
				"standard": "GB/T 22239-2019",
				"level":    "三级",
				"type":     "访问控制",
			},
		},
	}

	for _, rule := range rules {
		rm.rules[rule.ID] = &rule
	}
}

// initAuditRules 初始化安全审计类规则
func (rm *BuiltinRuleManager) initAuditRules() {
	rules := []SecurityRule{
		{
			ID:          "audit_001",
			Name:        "系统审计功能启用检查",
			Category:    "audit",
			Level:       "high",
			Description: "检查系统审计服务是否启用",
			Command:     "systemctl is-enabled auditd",
			CheckType:   "equals",
			Expected:    "enabled",
			Enabled:     true,
			Tags:        []string{"audit", "service", "等保三级"},
			Metadata: map[string]string{
				"standard": "GB/T 22239-2019",
				"level":    "三级",
				"type":     "安全审计",
			},
		},
		{
			ID:          "audit_002",
			Name:        "审计日志配置检查",
			Category:    "audit",
			Level:       "high",
			Description: "检查审计日志配置是否完整",
			Command:     "grep -E '^-w.*-p.*-k' /etc/audit/rules.d/audit.rules",
			CheckType:   "exists",
			Expected:    true,
			Enabled:     true,
			Tags:        []string{"audit", "log", "等保三级"},
			Metadata: map[string]string{
				"standard": "GB/T 22239-2019",
				"level":    "三级",
				"type":     "安全审计",
			},
		},
		{
			ID:          "audit_003",
			Name:        "日志轮转配置检查",
			Category:    "audit",
			Level:       "medium",
			Description: "检查系统日志轮转配置",
			Command:     "grep -E '^(rotate|size|compress)' /etc/logrotate.conf",
			CheckType:   "contains",
			Expected:    []string{"rotate", "size", "compress"},
			Enabled:     true,
			Tags:        []string{"log", "rotation", "等保三级"},
			Metadata: map[string]string{
				"standard": "GB/T 22239-2019",
				"level":    "三级",
				"type":     "安全审计",
			},
		},
		{
			ID:          "audit_004",
			Name:        "关键操作审计检查",
			Category:    "audit",
			Level:       "high",
			Description: "检查关键操作的审计规则配置",
			Command:     "auditctl -l | grep -E '(passwd|shadow|sudoers)'",
			CheckType:   "exists",
			Expected:    true,
			Enabled:     true,
			Tags:        []string{"audit", "critical", "等保三级"},
			Metadata: map[string]string{
				"standard": "GB/T 22239-2019",
				"level":    "三级",
				"type":     "安全审计",
			},
		},
		{
			ID:          "audit_005",
			Name:        "日志完整性保护检查",
			Category:    "audit",
			Level:       "medium",
			Description: "检查日志文件的完整性保护措施",
			Command:     "ls -la /var/log/audit/",
			CheckType:   "permission",
			Expected:    "600",
			Enabled:     true,
			Tags:        []string{"log", "integrity", "等保三级"},
			Metadata: map[string]string{
				"standard": "GB/T 22239-2019",
				"level":    "三级",
				"type":     "安全审计",
			},
		},
	}

	for _, rule := range rules {
		rm.rules[rule.ID] = &rule
	}
}
