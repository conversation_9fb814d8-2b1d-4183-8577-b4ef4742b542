# Linux等保基线核查工具 - 内置配置设计方案

## 1. 设计概述

将原本通过YAML/JSON外部配置文件管理的检查规则改为内置到Go代码中，通过结构体和常量定义的方式管理所有检查规则。

## 2. 技术架构调整

### 2.1 原架构问题
- 依赖外部YAML/JSON配置文件
- 需要配置文件解析库
- 配置文件可能被恶意修改
- 部署时需要管理多个配置文件

### 2.2 新架构优势
- 配置内置到二进制文件中
- 无需外部依赖
- 提高安全性和稳定性
- 简化部署和维护

## 3. 实现方案

### 3.1 核心数据结构设计

```go
// 检查规则结构体
type SecurityRule struct {
    ID          string            `json:"id"`
    Name        string            `json:"name"`
    Category    string            `json:"category"`
    Level       string            `json:"level"`
    Description string            `json:"description"`
    Command     string            `json:"command"`
    CheckType   string            `json:"check_type"`
    Expected    interface{}       `json:"expected"`
    Enabled     bool              `json:"enabled"`
    Tags        []string          `json:"tags"`
    Metadata    map[string]string `json:"metadata"`
}

// 规则分类
type RuleCategory struct {
    ID          string `json:"id"`
    Name        string `json:"name"`
    Description string `json:"description"`
    Icon        string `json:"icon"`
}
```

### 3.2 内置规则定义

将所有检查规则定义为Go常量和变量，按照等保要求的五大类别组织：

1. **身份鉴别类检查项**
2. **访问控制类检查项** 
3. **安全审计类检查项**
4. **系统安全类检查项**
5. **网络安全类检查项**

### 3.3 规则管理接口

```go
type RuleManager interface {
    GetAllRules() []SecurityRule
    GetRulesByCategory(category string) []SecurityRule
    GetRuleByID(id string) (*SecurityRule, error)
    GetEnabledRules() []SecurityRule
    UpdateRuleStatus(id string, enabled bool) error
    GetCategories() []RuleCategory
}
```

## 4. 实现细节

### 4.1 规则存储方式
- 使用Go的init()函数初始化规则
- 规则存储在内存中的map结构
- 支持运行时启用/禁用规则

### 4.2 规则配置管理
- 用户自定义配置存储在SQLite数据库
- 内置规则不可修改，只能启用/禁用
- 支持导入/导出用户配置

### 4.3 扩展性设计
- 预留自定义规则接口
- 支持插件式规则扩展
- 规则版本管理机制

## 5. 迁移策略

### 5.1 兼容性考虑
- 保持API接口不变
- 支持从YAML配置迁移到内置配置
- 提供配置导入工具

### 5.2 升级路径
1. 实现内置规则管理器
2. 保留YAML解析作为备用
3. 逐步迁移到内置配置
4. 移除YAML依赖

## 6. 优势分析

### 6.1 安全性提升
- 规则不可被外部修改
- 减少攻击面
- 提高系统完整性

### 6.2 性能优化
- 避免文件I/O操作
- 减少解析开销
- 提高启动速度

### 6.3 维护简化
- 单一二进制部署
- 减少配置管理复杂度
- 降低运维成本

## 7. 实施计划

### 7.1 第一阶段：基础框架
- 设计规则数据结构
- 实现规则管理器
- 创建内置规则定义

### 7.2 第二阶段：规则实现
- 实现身份鉴别类规则
- 实现访问控制类规则
- 实现安全审计类规则

### 7.3 第三阶段：完善功能
- 实现系统安全类规则
- 实现网络安全类规则
- 添加规则管理界面

### 7.4 第四阶段：测试优化
- 功能测试
- 性能测试
- 安全测试

## 8. 风险评估

### 8.1 潜在风险
- 规则更新需要重新编译
- 自定义规则功能受限
- 初期开发工作量较大

### 8.2 缓解措施
- 设计规则版本管理机制
- 保留插件扩展接口
- 分阶段实施降低风险

## 9. 总结

通过将配置内置化，可以显著提升系统的安全性、性能和可维护性。虽然会失去一些配置灵活性，但对于等保基线核查这种标准化场景，内置配置更加适合。
